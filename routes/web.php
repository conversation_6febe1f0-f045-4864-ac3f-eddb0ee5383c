<?php

use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use App\Livewire\Clubs\ClubsManagement;
use App\Livewire\Clubs\ClubForm;
use App\Livewire\Players\PlayersManagement;
use App\Livewire\Players\PlayerForm;
use App\Livewire\Cameras\CamerasManagement;
use App\Livewire\Cameras\CameraForm;
use App\Livewire\Commercials\CommercialsManagement;
use App\Livewire\Commercials\CommercialForm;
use App\Livewire\Sponsors\SponsorsManagement;
use App\Livewire\Sponsors\SponsorForm;
use App\Livewire\Events\EventsManagement;
use App\Livewire\Events\EventForm;
use App\Livewire\TokenTypes\TokenTypesManagement;
use App\Livewire\Invoicing\ClubInvoicing;
use App\Livewire\Invoicing\CommercialInvoicing;
use App\Livewire\Invoicing\ClubInvoicesStatus;
use App\Livewire\Clubs\ClubBalanceDetails;
use App\Livewire\AccountDeletion\AccountDeletionForm;
use App\Livewire\Auth\Login;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Route::get('/', Login::class)->name('login');

Route::get('dashboard', function() {
    // Redirect club users to their balance details
    if (Auth::guard('club_web')->check()) {
        return redirect()->route('club.balance-details');
    }

    // Redirect commercial users to their invoicing page
    if (Auth::guard('commercial_web')->check()) {
        return redirect()->route('commercial.invoicing.index');
    }

    return view('dashboard');
})
    ->middleware(['auth:admin', 'verified'])
    ->name('dashboard');

Route::middleware(['auth:admin', 'admin'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');

    // Clubs Management
    Route::get('clubs', ClubsManagement::class)->name('clubs.index');
    Route::get('clubs/create', ClubForm::class)->name('clubs.create');
    Route::get('clubs/{id}/edit', ClubForm::class)->name('clubs.edit');
    Route::get('clubs/{clubId}/token-types', TokenTypesManagement::class)->name('clubs.token-types');

    // Players Management
    Route::get('players', PlayersManagement::class)->name('players.index');
    Route::get('players/create', PlayerForm::class)->name('players.create');
    Route::get('players/{id}/edit', PlayerForm::class)->name('players.edit');
    Route::get('players/{id}/stats', App\Livewire\Players\PlayerStats::class)->name('players.stats');

    // Cameras Management
    Route::get('cameras', CamerasManagement::class)->name('cameras.index');
    Route::get('cameras/create', CameraForm::class)->name('cameras.create');
    Route::get('cameras/{id}/edit', CameraForm::class)->name('cameras.edit');

    // Commercials Management
    Route::get('commercials', CommercialsManagement::class)->name('commercials.index');
    Route::get('commercials/create', CommercialForm::class)->name('commercials.create');
    Route::get('commercials/{id}/edit', CommercialForm::class)->name('commercials.edit');

    // Sponsors Management
    Route::get('sponsors', SponsorsManagement::class)->name('sponsors.index');
    Route::get('sponsors/create', SponsorForm::class)->name('sponsors.create');
    Route::get('sponsors/{sponsorId}/edit', SponsorForm::class)->name('sponsors.edit');

    // Events Management
    Route::get('events', EventsManagement::class)->name('events.index');
    Route::get('events/create', EventForm::class)->name('events.create');
    Route::get('events/{eventId}/edit', EventForm::class)->name('events.edit');

    // Club Invoicing
    Route::get('invoicing', ClubInvoicing::class)->name('invoicing.index');
    Route::get('invoicing/status', ClubInvoicesStatus::class)->name('invoicing.status');



    // Club Balance Details (Admin can view any club)
    Route::get('clubs/{clubId}/balance-details', ClubBalanceDetails::class)->name('clubs.balance-details');

    // Admin routes
    Route::prefix('admin')->name('admin.')->middleware(['auth'])->group(function () {
        // Subscription plan management
        Route::resource('subscription-plans', App\Http\Controllers\Admin\SubscriptionPlanController::class);
    });
});

    // Commercial Invoicing (Admin access only from this route)
    Route::get('commercial-invoicing', CommercialInvoicing::class)->name('commercial-invoicing.index');

// Club routes (for club users accessing via web interface)
Route::middleware(['auth:club_web,admin'])->prefix('club')->name('club.')->group(function () {
    Route::get('balance-details', ClubBalanceDetails::class)->name('balance-details');
});

Route::post('club/logout', App\Livewire\Actions\ClubLogout::class)->middleware('auth:club_web')
    ->name('club.logout');

// Commercial routes (for commercial users accessing via web interface)
Route::middleware(['auth:commercial_web,admin'])->prefix('commercial')->name('commercial.')->group(function () {
    Route::get('invoicing', CommercialInvoicing::class)->name('invoicing.index');
});

Route::post('commercial/logout', App\Livewire\Actions\CommercialLogout::class)->middleware('auth:commercial_web')
    ->name('commercial.logout');

// Account Deletion Form - Public route for Play Store compliance
Route::get('account-deletion', AccountDeletionForm::class)->name('account.deletion');

// Privacy Policy - Public route for Play Store and App Store compliance
Route::view('privacy-policy', 'privacy-policy')->name('privacy.policy');

// Terms of Service - Public route for Play Store and App Store compliance
Route::view('terms-of-service', 'terms-of-service')->name('terms.service');

require __DIR__ . '/auth.php';
